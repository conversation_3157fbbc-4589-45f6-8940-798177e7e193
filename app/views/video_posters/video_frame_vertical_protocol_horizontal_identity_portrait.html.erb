<style>
    :root {

        /* Base sizes */
        --h1-base-size: 110px;
        --h2-base-size: 82px;
        --h1-base-gap: 22px;
        --h2-base-gap: 22px;
        --h2-container-base-padding: 130px;
        --party-contrast-text-color: white;
        /* Dynamic adjustments based on counts */
    <% if h2_urls.length <= 7 %> --h1-size: var(--h1-base-size);
    <% else %> --h1-size: calc(var(--h1-base-size) * 0.9); /* Slightly smaller for more h2s */
    <% end %><% if h2_urls.length <= 4 %> --h2-size: var(--h2-base-size);
        --h2-gap: var(--h2-base-gap);
        --h1-gap: var(--h1-base-gap);
        --h2-container-padding: var(--h2-container-base-padding);
    <% elsif h2_urls.length <= 6 %> --h2-size: calc(var(--h2-base-size) * 1.05); /* Slightly larger for fewer h2s */
        --h2-gap: 30px;
        --h1-gap: 30px;
        --h2-container-padding: calc(var(--h2-container-base-padding) / 2);
    <% elsif h2_urls.length <= 8 %> --h2-size: calc(var(--h2-base-size) * 0.85);
        --h2-gap: 18px;
        --h1-gap: 18px;
        --h2-container-padding: calc(var(--h2-container-base-padding) / 3);
    <% else %> --h2-size: calc(var(--h2-base-size) * 0.6);
        --h2-gap: 18px;
        --h1-gap: 18px;
        --h2-container-padding: 0px; /* No padding for many h2s */
    <% end %><% if h1_urls.length == 1 && h2_urls.length <= 4 %> --h2-size: calc(var(--h2-base-size) * 1.1);
        --h2-gap: 30px;
        --h1-gap: 30px;
        --h2-container-padding: calc(var(--h2-container-base-padding) / 2);
    <% end %><% if h1_urls.length == 0 %> --h2-container-padding: var(--h2-container-base-padding);
    <% end %> --container-padding: 5px;
        box-sizing: border-box;
        font-family: "Anek Telugu", "Poppins", sans-serif;


    <% if party_id == 31403 %> --party-gradient: linear-gradient(180deg, #0266B4 0%, #22BBB8 55.39%, #008E46 100%);
        --party-footer-bg-gradient: linear-gradient(180deg, #087748 22.06%, rgba(8, 119, 72, 0.0) 97.79%);
        --party-contrast-text-color: #121212;
    <% elsif party_id == 31402 %> --party-gradient: linear-gradient(135deg, #F6BD00 0%, #F6BD00 21.63%, #E36D1E 60.31%, #D32030 86.64%);
        --party-footer-bg-gradient: linear-gradient(180deg, #C90807 22.06%, rgba(219, 39, 38, 0.0) 97.79%);
        --party-contrast-text-color: #121212;
    <% elsif party_id == 31406 %> --party-gradient: linear-gradient(308.32deg, #cc0000 43.08%, #ffb0b0 105.21%);
        --party-footer-bg-gradient: linear-gradient(180deg, #820707 22.06%, rgba(130, 7, 7, 0) 97.79%);
        --party-contrast-text-color: #ffed91;
    <% elsif party_id == 31401 || party_id == 37967 %> --party-gradient: linear-gradient(
            149.88deg,
            #f37022 4.18%,
            #e5fff7 52.37%,
            #0f823f 98.51%
    );
        --party-footer-bg-gradient: linear-gradient(
                180deg,
                #045c2b 24.48%,
                rgba(4, 92, 43, 0) 98.69%
        );
        --party-contrast-text-color: #121212;
    <% elsif party_id == 31398 || party_id == 37788 %> --party-gradient: rgba(243, 114, 22, 1);
        --party-footer-bg-gradient: linear-gradient(180deg, #AA2D05 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
        --party-contrast-text-color: #121212;
    <% elsif party_id == 31405 %> --party-gradient: linear-gradient(326.47deg, #F40592 3.06%, #FFBDE5 80.06%, #F47FC5 99.75%);
        --party-footer-bg-gradient: linear-gradient(180deg, #AD0969 22.06%, rgba(173, 9, 105, 0.0) 97.79%);
        --party-contrast-text-color: #121212;
    <% else %> --party-gradient: linear-gradient(45deg, #0061FF, #A1DDFF);
        --party-footer-bg-gradient: linear-gradient(180deg, #0061FF 22.06%, rgba(170, 45, 5, 0.0) 97.79%);
        --party-contrast-text-color: #121212;
    <% end %>

    }

    .video-frame {
        position: relative;
        width: 720px;
        height: 900px;
        z-index: 0;
        /*background: var(--party-gradient);*/
    }

    .photo {
        width: 258px;
        height: 287px;
        z-index: 5;
        flex-shrink: 0;
    }

    .video-frame .photo img {
        width: 100%;
        height: 100%;
        mask-image: linear-gradient(to bottom, white, white 80%, transparent);
    }

    .identity-details {
        flex: 1;
        height: 108px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: start;
        bottom: 2px;
        left: 80px;
        min-width: 0;
    }

    .identity-details h1 {
        margin: 0;
        width: 100%;
        --webkit-line-clamp: 1;
        line-clamp: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    <% if Unicode::DisplayWidth.of(name) <= 16 %> font-size: 32px;
        line-height: 32px;
    <% elsif Unicode::DisplayWidth.of(name) <= 26 %> font-size: 22px;
        line-height: 22px;
    <% else %> font-size: 19px;
        line-height: 19px;
    <% end %> color: var(--party-contrast-text-color);
        margin: 8px 0px 8px 24px;
    }

    .identity-container {
        box-sizing: border-box;
        position: absolute;
        bottom: 0;
        display: flex;
        align-items: end;
        left: 0;
        right: 0;
    }

    .identity-details h4 {
        color: var(--party-contrast-text-color);
        margin: 0px 0px 10px 24px;
        font-size: 19px;
        line-height: 19px;
        font-weight: 600;
    }

    .party-icon {
        margin-left: 12px;
    }

    .party-icon img {
        width: 80px;
        height: 100px;
        z-index: 2;
        bottom: 0px;
        padding: 0px 0px 0px 2px;
    }

    .protocol-container {
        position: absolute;
        width: 35%;
        display: flex;
        flex-direction: column;
        margin-top: 30px;
        right: 0px;
        max-height: 500px;
    }

    .protocol {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        gap: var(--h1-gap);
        flex-direction: column;
    }

    .protocol .h2-outer-container {
        flex: 0;
    }


    .protocol .h2-outer-container {
        flex-direction: row;
    }

    .protocol .h2-outer-container .h2-inner-container {
        flex-direction: column;
    }

    .double-decker-even .h2-outer-container .h2-inner-container:first-child {
        padding-bottom: calc(var(--h2-size) - var(--h2-gap));
    }

    .double-decker-even .h2-outer-container .h2-inner-container:nth-child(2) {
        padding-top: calc(var(--h2-size) - var(--h2-gap));
    }

    .protocol .h2-spaced-container {
        display: flex;
        flex-direction: column;

        flex: 1;
        align-items: center;
        justify-content: space-evenly;
        padding: 0 var(--h2-container-padding);
    }


    .protocol.no-flex .h2-spaced-container {
        flex: 0;
        gap: var(--h2-gap);
    }


    .h2-outer-container {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .h2-inner-container {
        display: flex;
        flex-direction: row;
        gap: var(--h2-gap);
        box-sizing: border-box;
    }

    .protocol.no-flex {
        justify-content: start;
    }

    .protocol.tight {
        --h2-container-padding: 120px;
    }

    .no-flex .h2-outer-container {
        flex: 0;
    }

    .leader {
        width: var(--h1-size);
        height: var(--h1-size);
        background: radial-gradient(closest-side, rgba(255, 255, 255, 0.65), transparent);
    }

    .leader.small {
        width: var(--h2-size);
        height: var(--h2-size);
    }

    .leader img {
        width: 100%;
        height: 100%;
        mask-image: radial-gradient(closest-side, white, white 75%, transparent);
    }

</style>

<div class="video-frame">
  <div class="identity-container">
    <% if party_icon_url.present? %>
      <div class="party-icon"><img src="<%= party_icon_url %>" alt=""></div>
    <% end %>
    <div class="identity-details">
      <h1 id="user-name"><%= name %></h1>
      <% if role.present? %>
        <h4><%= role %></h4>
      <% end %>
    </div>

    <div class="photo"><img src="<%= photo_url %>" alt=""></div>
  </div>
  <div class="protocol-container">
    <div class="protocol <%= protocol_classnames %>">
      <% if h2_urls.blank? %>
        <!-- loop through h1s -->
        <% h1_urls.each do |url| %>
          <div class="leader">
            <img src="<%= url %>" alt="">
          </div>
        <% end %>
      <% elsif h1_urls.present? && h1_urls.length < 3 %>
        <div class="leader">
          <img src="<%= h1_urls.first %>" alt="">
        </div>

        <% if h2_urls.length <= 4 %>
          <div class="h2-spaced-container">
            <!-- loop through h2s -->
            <% h2_urls.each do |url| %>
              <div class="leader small">
                <img src="<%= url %>" alt="">
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="h2-outer-container">
            <div class="h2-inner-container">
              <!-- loop through odd index h2s -->
              <% h2_urls.each_with_index do |url, index| %>
                <% if index < ((h2_urls.length + 1) / 2) %>
                  <div class="leader small">
                    <img src="<%= url %>" alt="">
                  </div>
                <% end %>
              <% end %>
            </div>
            <div class="h2-inner-container">
              <!-- loop through even index h2s -->
              <% h2_urls.each_with_index do |url, index| %>
                <% if index >= ((h2_urls.length + 1) / 2) %>
                  <div class="leader small">
                    <img src="<%= url %>" alt="">
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        <% end %>

        <% if h1_urls.length == 2 %>
          <div class="leader">
            <img src="<%= h1_urls.second %>" alt="">
          </div>
        <% end %>
      <% end %>
    </div>
  </div>
</div>
