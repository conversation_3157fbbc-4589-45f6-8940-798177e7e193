# frozen_string_literal: true

# Currently we are just generating the HTMLs, capturing for debugging purposes
# Once the schemas are added, will add the necessary rows to video_frames table
class GenerateVideoFrames
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Capture
  sidekiq_options queue: :default, retry: 0, lock: :until_and_while_executing, on_conflict: :log
  sidekiq_throttle(
    concurrency: {
      limit: 2,
    },
    threshold: {
      limit: 5,
      period: 5.second,
    }
  )

  def perform(user_id)
    Honeybadger.context({ user_id: user_id })
    user = User.find(user_id)
    return unless user.present?

    VideoFrame.where(user_id: user_id).update_all(active: false)

    VideoFrameTemplates::BlackProtocolAndWhiteIdentity.generate(user)
    VideoFrameTemplates::BlackProtocolAndFlippedWhiteIdentity.generate(user)
    VideoFrameTemplates::VerticalProtocolHorizontalIdentityPortrait.generate(user)
    VideoFrameTemplates::FlippedVerticalProtocolHorizontalIdentityPortrait.generate(user)
    VideoFrameTemplates::TransparentIdentityOverVideo.generate(user)
    VideoFrameTemplates::TransparentFlippedIdentityOverVideo.generate(user)
    VideoFrameTemplates::TransparentIdentityAndTransparentProtocolOverVideo.generate(user)
    VideoFrameTemplates::TransparentFlippedIdentityAndTransparentProtocolOverVideo.generate(user)

  end
end
