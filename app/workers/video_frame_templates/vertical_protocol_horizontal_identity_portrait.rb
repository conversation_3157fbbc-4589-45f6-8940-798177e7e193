module VideoFrameTemplates
  class VerticalProtocolHorizontalIdentityPortrait
    def self.generate(user)
      user_poster_layout = user.get_user_poster_layout
      return unless user_poster_layout.present?

      name = user.name
      role = user.get_badge_role&.get_description
      photo_url = user.poster_photo&.url
      affiliated_party_id = user.affiliated_party_circle_id.to_i
      party_icon_url = user.get_affiliated_party_icon(affiliated_party_id) if affiliated_party_id.positive?

      user_leader_photos = UserLeaderPhoto.where(user_poster_layout_id: user_poster_layout.id)
      h1_urls = user_leader_photos
                  .select { |ulp| ulp.header_type == 'header_1' }
                  .map { |ulp| compressed_url(ulp.photo, size: 240, quality: 100) }
      h2_urls = user_leader_photos
                  .select { |ulp| ulp.header_type == 'header_2' }
                  .map { |ulp| compressed_url(ulp.photo, size: 120, quality: 100) }

      protocol_classnames = ''
      protocol_classnames += ' tight' if h1_urls.blank?
      protocol_classnames += ' no-flex' if h1_urls.length == 1
      protocol_classnames += ' double-decker-even' if h2_urls.length.even? && h2_urls.length > 4
      protocol_classnames += ' double-decker-odd' if h2_urls.length.odd? && h2_urls.length > 4

      data = {
        name: name,
        role: role,
        photo_url: photo_url,
        party_icon_url: party_icon_url,
        party_id: affiliated_party_id,
        h1_urls: h1_urls,
        h2_urls: h2_urls,
        protocol_classnames: protocol_classnames,
      }

      html = potrait_html(data)

      uploaded_image = Capture.capture_html_as_image(html, '.video-frame', true)

      raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

      uploaded_image_url = uploaded_image['cdn_url']

      json_data = {
        name: 'vertical_protocol_horizontal_identity_portrait',
        frame_url: uploaded_image_url,
        frame_width: uploaded_image['width'],
        frame_height: uploaded_image['height'],
        safe_area: {
          anchor_point: { x: 0, y: 0 },
          width: 556,
          height: 750,
        },
      }

      VideoFrame.create(
        frame_type: :video_over_frame,
        supported_video_type: :portrait,
        user_id: user.id,
        data: json_data,
        active: true
      ).save!
    end

    def self.compressed_url(photo, size:, quality: 100)
      # Photo
      photo.compressed_url(size: size, quality: quality, fit: false) if photo.is_a?(Photo)

      # Admin   Medium
      photo.compressed_url(size: size, quality: quality)
    end

    def self.potrait_html(data)
      ActionController::Base.render(
        inline: File.read(Rails.root.join('app', 'views', 'video_posters', 'video_frame_vertical_protocol_horizontal_identity_portrait.html.erb')),
        locals: data
      )
    end
  end
end
