module VideoFrameTemplates
  class PartyGradientsFlippedIdentityOverVideo

    def self.generate(user)
      user_poster_layout = user.get_user_poster_layout
      return unless user_poster_layout.present?

      identity_image_url = generate_identity_image_url(user)

      data = {
        name: 'party_gradients_flipped_identity_over_video',
        identity_photo_url: identity_image_url,
        identity_width: 720,
        identity_height: 267,
        identity_overlap_height: 159,
      }

      VideoFrame.create(
        frame_type: :identity_over_video,
        supported_video_type: :portrait,
        user_id: user.id,
        data: data,
        active: true
      )

    end

    private

    def self.generate_identity_image_url(user)
      name = user.name
      role = user.get_badge_role&.get_description
      photo_url = user.poster_photo&.url
      affiliated_party_id = user.affiliated_party_circle_id.to_i
      party_icon_url = user.get_affiliated_party_icon(affiliated_party_id) if affiliated_party_id.positive?

      data = {
        name: name,
        role: role,
        photo_url: photo_url,
        party_icon_url: party_icon_url,
        party_id: affiliated_party_id,
        identity_strip_background: 'party-gradients'
      }

      html = identity_html(data)
      uploaded_image = Capture.capture_html_as_image(html, '.identity-container', true)
      raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

      uploaded_image_url = uploaded_image['cdn_url']
      uploaded_image_url
    end

    def self.identity_html(data)
      ActionController::Base.render(
        inline: File.read(Rails.root.join('app', 'views', 'video_posters', 'video_frame_horizontal_flipped_identity.html.erb')),
        locals: data
      )
    end
  end
end
