module VideoFrameTemplates
  class HorizontalProtocol
    def self.generate(user, user_poster_layout)
      user_leader_photos = UserLeaderPhoto.where(user_poster_layout_id: user_poster_layout.id)
      h1_urls = user_leader_photos
        .select { |ulp| ulp.header_type == 'header_1' }
        .map { |ulp| compressed_url(ulp.photo, size: 240, quality: 100) }
      h2_urls = user_leader_photos
        .select { |ulp| ulp.header_type == 'header_2' }
        .map { |ulp| compressed_url(ulp.photo, size: 120, quality: 100) }

      protocol_classnames = ''
      protocol_classnames += ' no-flex' if h1_urls.length == 1
      protocol_classnames += ' double-decker-even' if h2_urls.length.even? && h2_urls.length > 4
      protocol_classnames += ' double-decker-odd' if h2_urls.length.odd? && h2_urls.length > 4

      data = {
        h1_urls: h1_urls,
        h2_urls: h2_urls,
        protocol_classnames: protocol_classnames
      }

      html = protocol_html(data)
      uploaded_image = Capture.capture_html_as_image(html, '.protocol-container', true)
      raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

      uploaded_image_url = uploaded_image['cdn_url']
      uploaded_image_width = uploaded_image['width']
      uploaded_image_height = uploaded_image['height']
      { uploaded_image_url:, uploaded_image_width:, uploaded_image_height: }
    end

    private

    def self.compressed_url(photo, size:, quality: 100)
      # Photo
      photo.compressed_url(size: size, quality: quality, fit: false) if photo.is_a?(Photo)

      # AdminMedium
      photo.compressed_url(size: size, quality: quality)
    end

    def self.protocol_html(data)
      ActionController::Base.render(
        inline: File.read(Rails.root.join('app', 'views', 'video_posters', 'video_frame_horizontal_protocol.html.erb')),
        locals: data
      )
    end
  end
end
