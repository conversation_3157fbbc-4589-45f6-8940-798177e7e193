class VideoPostersController < ApiController
  before_action :set_logged_in_user, except: [:video_poster_generation_failed, :video_poster_generation_completed]

  def show
    video_poster_id = params[:id]

    if video_poster_id.nil?
      render json: { message: I18n.t('errors.missing_video_poster_id') }, status: :bad_request
      return
    end

    video_poster = VideoPoster.includes(:video_frame, :video).find_by(id: video_poster_id)
    if video_poster.nil?
      render json: { message: I18n.t('errors.video_poster_not_found') }, status: :not_found
      return
    end

    render json: video_poster.get_json, status: :ok
  end

  def generate_video_poster
    video_frame_id = params[:video_frame_id]
    video_id = params[:video_id]

    if video_frame_id.nil?
      render json: { message: I18n.t('errors.missing_video_frame_id') }, status: :bad_request
      return
    end

    if video_id.nil?
      render json: { message: I18n.t('errors.missing_video_id') }, status: :bad_request
      return
    end

    video_frame = VideoFrame.find_by(id: video_frame_id)
    if video_frame.nil?
      render json: { message: I18n.t('errors.video_frame_not_found') }, status: :not_found
      return
    end

    video = Video.find_by(id: video_id)
    if video.nil?
      render json: { message: I18n.t('errors.video_not_found') }, status: :not_found
      return
    end

    video_poster = VideoPoster.find_by(video_frame_id: video_frame_id, source_video_id: video_id)
    if video_poster.present?
      unless video_poster.failed?
        render json: video_poster.get_json, status: :ok
        return
      end
    end
    video_poster = VideoPoster.create_or_find_by(video_frame_id: video_frame_id, source_video_id: video_id, user_id: @user.id)

    if video_poster.may_process?
      lambda_payload =
        {
          video_url: video.source_url,
          layout_data: video_frame.data,
          layout_type: video_frame.frame_type,
          job_id: video_poster.job_id,
          callback_url: Constants.get_api_host + '/video-posters',
        }

      VideoPostersGenerationTrigger.perform_async(lambda_payload, video_poster.id, video_poster.job_id)
    end
    render json: video_poster.get_json, status: :ok
  end

  def video_poster_generation_failed
    job_id = params[:job_id]

    if job_id.nil?
      render json: { message: I18n.t('errors.job_id_missing') }, status: :bad_request
      return
    end

    video_poster = VideoPoster.find_by(job_id: job_id)
    if video_poster.nil?
      render json: { message: I18n.t('errors.video_poster_not_found') }, status: :not_found
      return
    end

    video_poster.fail!(params[:error_code]) if video_poster.may_fail?
    render json: { video_poster_id: video_poster.id, job_id: video_poster.job_id, status: video_poster.status }, status: :ok
  end

  def video_poster_generation_completed
    job_id = params[:job_id]

    if job_id.nil?
      render json: { message: I18n.t('errors.job_id_missing') }, status: :bad_request
      return
    end

    video_poster = VideoPoster.find_by(job_id: job_id)
    if video_poster.nil?
      render json: { message: I18n.t('errors.video_poster_not_found') }, status: :not_found
      return
    end

    video_poster.complete! if video_poster.may_complete?
    render json: { video_poster_id: video_poster.id, job_id: video_poster.job_id, status: video_poster.status }, status: :ok
  end
end
