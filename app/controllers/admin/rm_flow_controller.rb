class Admin::RmFlowController < Admin::AdminController
  include J<PERSON><PERSON><PERSON>ualAuth

  before_action :set_user, except: [:party_circles, :protocol_xy_positions]
  before_action :check_dual_auth
  skip_before_action :verify_authenticity_token, only: [:save_layout_as_draft]

  def party_circles
    # Apply filters based on query params
    circles = Circle.where(active: true, level: :political_party)
                    .ransack(m: 'or', id_start: params[:term], name_en_cont: params[:term],
                             short_name_cont: params[:term])
                    .result

    render json: circles.map { |record| record.get_json_for_rm_layout_creation(poster_photos_required: false) },
           status: :ok
  end

  def protocol_xy_positions
    h1_count = params[:h1_count].to_i
    h2_count = params[:h2_count].to_i

    layout_key = "layout_#{h1_count}_#{h2_count}".to_sym

    layout = UserPosterLayout::LAYOUTS[layout_key]
    # In posters v3, we enabled the frames which are in ratio of 4:5,so we are multiplying it by 4/5 to handle the
    # position_y and radius
    photo_position_hash = []
    if layout.blank?
      return render json: { message: "Layout with this headers count not found,
                                        Valid layout types are: #{UserPosterLayout::LAYOUTS.keys}" },
                    status: :not_found
    end

    layout[:photos].each do |photo_details|
      photo_position_hash << {
        type: photo_details[:type],
        position_x: photo_details[:position_x],
        position_y: photo_details[:position_y] * 4.0 / 5.0,
        radius: photo_details[:radius] * 4.0 / 5.0
      }
    end

    render json: photo_position_hash, status: :ok
  end

  def get_layout_creation_info
    return render json: { message: I18n.t('errors.user_not_found') }, status: :not_found if @user.blank?
    premium_pitch = @user.premium_pitch
    if premium_pitch.blank?
      return render json: { message: I18n.t('errors.jathara.not_a_lead') }, status: :not_found
    end
    if premium_pitch.trail_enabled?
      return render json: { message: I18n.t('errors.jathara.layout_already_enabled') }, status: :conflict
    end
    if premium_pitch.is_pending_oe_review?
      return render json: { message: I18n.t('errors.jathara.pending_oe_review') }, status: :conflict
    end

    # jathara enabled on interested, rm_draft, pending_layout_approval
    if @user.rm_workflow_enabled?
      poster_layout = @user.get_user_poster_layout_including_inactive

      # Check if user has ever been in rm_submitted state
      has_been_rm_submitted = user_has_been_rm_submitted?

      # Get current referrer_id if exists
      referrer_id = @user.get_user_referrer_id

      render json: {
        user: @user.user_json_for_rm_layout_creation_tool,
        remarks: @user.get_layout_remarks(poster_layout),
        badge_free_text: @user.get_badge_free_text_for_rm_layout_creation,
        protocol_leader_circles: @user.get_affiliated_circles_for_rm_layout_creation(poster_layout),
        user_poster_photos: @user.get_layout_original_identity_photos,
        enable_referrer_id: !has_been_rm_submitted,
        referrer_id: referrer_id,
        family_frame_name: @user.family_frame_name,
        selected_no_affiliated_party: fetch_selected_no_affiliated_party
      }.compact, status: :ok
    else
      render json: { message: I18n.t('errors.jathara.layout_already_enabled') }, status: :conflict
    end
  end

  def search_circles_for_protocol
    return render json: { message: I18n.t('errors.user_not_found') }, status: :not_found if @user.blank?
    return render json: { message: I18n.t('errors.search_text_blank') }, status: :not_found if params[:term].blank?

    circles = Circle.search_circles_for_protocol(user: @user, term: params[:term])
    return render json: [], status: :ok if circles.blank?

    protocol_data = circles.map(&:get_json_for_rm_layout_creation)
    poster_layout = @user.get_user_poster_layout_including_inactive

    leader_photo_ids = if poster_layout.present?
                         poster_layout.user_leader_photos.order(:header_type, :priority).pluck(:photo_id)
                       else
                         []
                       end

    # add selected true for circle json poster photos id matched with leader photo ids
    protocol_data.each do |circle|
      selected_set = false

      circle[:poster_photos].each do |photo|
        if leader_photo_ids.include?(photo[:id])
          photo[:selected] = true
          selected_set = true
        else
          photo[:selected] = false
        end
      end

      # If no photo was matched/selected, mark the first photo as selected
      if !selected_set && circle[:poster_photos].any?
        circle[:poster_photos].first[:selected] = true
      end
    end

    render json: protocol_data, status: :ok
  end

  def save_layout_as_draft
    # Get the status from params
    status = params[:status]
    premium_pitch = @user.premium_pitch
    referrer_id = params[:referrer_id]

    # Validate selected_no_affiliated_party and poster_affiliated_party_id
    selected_no_affiliated_party = ActiveModel::Type::Boolean.new.cast(params[:selected_no_affiliated_party])
    if status != "rm_draft" && selected_no_affiliated_party && params[:poster_affiliated_party_id].present?
      return render json: { message: I18n.t('errors.jathara.invalid_affiliated_party_selection') }, status: :unprocessable_entity
    elsif status != "rm_draft" && !selected_no_affiliated_party && params[:poster_affiliated_party_id].blank?
      return render json: { message: I18n.t('errors.jathara.affiliated_party_required') }, status: :unprocessable_entity
    end

    # Variables to store the saved data for comparison
    saved_badge_text = nil
    saved_user_poster_photos = {}
    saved_protocol_leader_circles = []

    ActiveRecord::Base.transaction do
      update_user_name
      saved_badge_text = update_badge_free_text

      # Handle selected_no_affiliated_party and poster_affiliated_party_id
      save_affiliated_party_selection(selected_no_affiliated_party)

      @user.update(dob: params[:dob]) if params[:dob].present? && params[:dob].to_date != @user.dob

      # Handle referrer_id - only create if user has never been in rm_submitted state
      has_been_rm_submitted = user_has_been_rm_submitted?
      if referrer_id.present? && !has_been_rm_submitted
        user_referral = UserReferral.where(referred_user_id: @user.id).first_or_initialize
        user_referral.assign_attributes(user_id: referrer_id)
        user_referral.save if user_referral.changed?
      end

      # Process and save user poster photos with or without validations based on status
      if status == "rm_draft"
        # For rm_draft status, no validations required
        saved_user_poster_photos = process_and_save_user_poster_photos(skip_validations: true)
      elsif status == "rm_submitted"
        # For rm_submitted status, validations are required
        saved_user_poster_photos = process_and_save_user_poster_photos(skip_validations: false)
      else
        # Default behavior for other statuses
        saved_user_poster_photos = process_and_save_user_poster_photos
      end

      saved_protocol_leader_circles = process_and_save_protocol_leader_circles
    end

    # trigger background removal for poster photos
    # This is done after the transaction to ensure that the photos are saved before triggering the background removal
    trigger_remove_background_for_poster_photos

    response_message = nil

    # Trigger BOE and/or OE workflows
    if status == "rm_draft"
      premium_pitch.rm_drafted! if premium_pitch&.may_rm_drafted?
      response_message = I18n.t('jathara.layout_draft_saved')
    elsif status == "rm_submitted"
      um = UserMetadatum.find_or_initialize_by(user: @user, key: Constants.last_rm_submitted_data)

      # Get the previous saved data
      last_params = {}
      last_params = JSON.parse(um.value, symbolize_names: true) if um.value.present?

      # Track whether OE review has been completed previously
      oe_review_completed = false
      oe_status_metadata = UserMetadatum.find_or_initialize_by(user: @user, key: Constants.oe_review_completed)
      oe_review_completed = ActiveModel::Type::Boolean.new.cast(oe_status_metadata.value) if oe_status_metadata.value.present?

      # Compare previously saved data with current saved data
      has_boe_update = params[:badge_free_text].present? && saved_badge_text != last_params[:badge_free_text]

      # Compare photo IDs for user poster photos
      previous_user_poster_photos = last_params[:user_poster_photos] || {}
      previous_photo_ids = extract_photo_ids_from_photos(previous_user_poster_photos)
      current_photo_ids = extract_photo_ids_from_photos(saved_user_poster_photos)
      user_photos_changed = previous_photo_ids != current_photo_ids

      # Consider protocol circles changed if any new circle creation is requested
      protocol_circles_changed = saved_protocol_leader_circles.filter { |c| c[:circle_id].blank? }.any?

      # Determine if OE update is needed - either changes detected or OE review never completed
      has_oe_update = (user_photos_changed || protocol_circles_changed || !oe_review_completed)

      # Update premium pitch based on changes
      if has_boe_update && has_oe_update
        premium_pitch.setup_badge! if premium_pitch&.may_setup_badge?
        response_message = I18n.t('jathara.layout_submitted_for_boe_oe_review')
      elsif has_boe_update && !has_oe_update
        premium_pitch.setup_badge_no_layout_setup! if premium_pitch&.may_setup_badge_no_layout_setup?
        response_message = I18n.t('jathara.layout_submitted_for_boe_oe_review')
      elsif !has_boe_update && has_oe_update
        premium_pitch.setup_layout! if premium_pitch&.may_setup_layout?
        response_message = I18n.t('jathara.layout_submitted_for_boe_oe_review')
      else
        premium_pitch.send_for_layout_approval! if premium_pitch&.may_send_for_layout_approval?
        response_message = I18n.t('jathara.layout_sent_for_user_approval')
      end

      # Save current params for future comparison
      current_data = {
        "badge_free_text" => saved_badge_text,
        "user_poster_photos" => saved_user_poster_photos,
        "protocol_leader_circles" => saved_protocol_leader_circles
      }
      um.value = current_data.to_json
      um.save!
    end

    render json: { message: response_message }, status: :ok
  rescue UnsupportedImageFormatError => e
    # Don't notify Honeybadger for unsupported image format errors
    # return the error message to the client
    return render json: { message: e.message }, status: :unprocessable_entity
  rescue ActiveRecord::RecordInvalid => invalid
    Honeybadger.notify("save layout as draft data invalid",
                       context: {
                         user_id: @user.id,
                         params: params,
                         error_message: invalid.record.errors.full_messages.join(', ')
                       })
    return render json: { message: invalid.record.errors.full_messages.join(', ') },
                  status: :unprocessable_entity
  rescue => e
    Honeybadger.notify(e, context: { user_id: @user.id })
    return render json: { message: 'something went wrong' }, status: :internal_server_error
  end

  private

  def save_affiliated_party_selection(selected_no_affiliated_party)
    # Save selected_no_affiliated_party flag
    metadatum = UserMetadatum.find_or_initialize_by(user: @user, key: Constants.selected_no_affiliated_party_key)
    metadatum.value = selected_no_affiliated_party.to_s
    metadatum.save!

    # Update poster_affiliated_party_id based on the flag
    if selected_no_affiliated_party
      @user.update_poster_affiliated_party_id(nil)
    else
      @user.update_poster_affiliated_party_id(params[:poster_affiliated_party_id])
    end
  end

  def arrays_of_nested_hashes_equal?(arr1, arr2)
    arr1 = arr1 || []
    arr2 = arr2 || []

    return false unless arr1.is_a?(Array) && arr2.is_a?(Array)

    normalize = ->(arr) { arr.map { |h| deep_sort(h) } }
    Set.new(normalize.call(arr1)) == Set.new(normalize.call(arr2))
  end

  def deep_sort(obj)
    case obj
    when Hash
      obj.map { |k, v| [k, deep_sort(v)] }.sort.to_h
    when Array
      obj.map { |v| deep_sort(v) }.sort_by(&:to_s)
    else
      obj
    end
  end

  def extract_photo_ids_from_photos(photos)
    result = []
    return result if photos.blank?

    photos.each do |_, photo_data|
      result << photo_data[:photo_id] if photo_data[:photo_id].present?
    end

    result.sort
  end

  def update_user_name
    @user.update(name: params[:user_name]) if params[:user_name].present? && params[:user_name] != @user.name
  end

  def update_badge_free_text
    if params[:badge_free_text].blank?
      UserMetadatum.where(user: @user, key: Constants.badge_free_text_key).destroy_all
      return nil
    end

    met = UserMetadatum.find_or_initialize_by(user: @user, key: Constants.badge_free_text_key)

    if met.value != params[:badge_free_text]
      met.value = params[:badge_free_text]
      met.save!
    end

    # Return the value for comparison
    params[:badge_free_text]
  end

  def process_and_save_user_poster_photos(skip_validations: false)
    valid_types = User::VALID_ORIGINAL_POSTER_PHOTO_KEYS
    used_user_profile_as_cutout_photo = ActiveModel::Type::Boolean.new.cast(params[:used_user_profile_as_cutout_photo]) || false
    saved_photos = {}

    # Mandatory check: poster photo without background must be provided.
    # Skip this validation if skip_validations is true and user_poster_photos are blank
    return saved_photos if params[:user_poster_photos].blank? && skip_validations

    if !skip_validations && (params[:user_poster_photos].blank? ||
      params[:user_poster_photos].values.none? do |pd|
        pd[:type] == Constants.poster_photo_without_background_original_key
      end)
      raise ApiError.new("poster photo without background is mandatory", :bad_request)
    end

    # Get existing photo metadata to track what needs to be deleted
    existing_photo_metadata = UserMetadatum.where(user: @user, key: valid_types)
    existing_photo_types = existing_photo_metadata.pluck(:key)
    new_photo_types = params[:user_poster_photos].values.map { |pd| pd[:type] }
    # Save family_frame_name if provided
    has_family_frame_photo = false

    # Process new/updated photos
    params[:user_poster_photos].values.each do |photo_data|
      key = photo_data[:type]

      # Validate photo type before processing
      unless valid_types.include?(key)
        raise ApiError.new("invalid photo type: #{key}", :bad_request)
      end

      # Check if this is a family frame photo
      has_family_frame_photo = true if key == Constants.family_frame_photo_original_key

      photo = nil

      # Determine the photo to use based on the scenario
      if key == Constants.poster_photo_without_background_original_key && used_user_profile_as_cutout_photo
        # Use user's profile photo for cutout
        photo = @user.photo

        # Skip if profile photo is missing
        if photo.nil?
          Rails.logger.warn("Profile photo not found for user #{@user.id}")
          next
        end
      else
        # Process uploaded photo or existing photo
        if photo_data[:photo_file].present?
          # New photo uploaded
          # Check if the uploaded photo has a supported image format
          file_extension = File.extname(photo_data[:photo_file].original_filename).downcase
          unless Photo::SUPPORTED_IMAGE_FORMATS.include?(file_extension)
            background_removal_key = User::POSTER_PHOTO_KEY_MAPPING[key]
            error_message = I18n.t('errors.unsupported_image_format',
                                   file_format: file_extension,
                                   key: background_removal_key,
                                   supported_formats: Photo::SUPPORTED_IMAGE_FORMATS.join(', '))
            raise UnsupportedImageFormatError.new(error_message)
          end

          photo = Photo.new(blob_data: photo_data[:photo_file], user: @user, service: :aws)
          # Skip to next photo if save fails
          unless photo.save
            Rails.logger.warn("Failed to save new photo for user #{@user.id}, type: #{key}")
            next
          end
        elsif photo_data[:photo].present?
          # Existing photo selected
          photo_data_parsed = JSON.parse(photo_data[:photo], symbolize_names: true)
          photo = Photo.find_by(id: photo_data_parsed[:id])
          # Skip if photo not found
          if photo.nil?
            Rails.logger.warn("Photo not found with ID: #{photo_data_parsed[:id]} for user #{@user.id}, type: #{key}")
            next
          end
        else
          # No photo provided for this type
          Rails.logger.warn("No photo provided for user #{@user.id}, type: #{key}")
          next
        end
      end

      # Update or create user metadata for this photo type
      um = UserMetadatum.find_or_create_by(user: @user, key: key)

      # If the photo ID has changed, update metadata and queue background removal
      if um.value != photo.id.to_s
        um.update(value: photo.id)
        background_removal_key = User::POSTER_PHOTO_KEY_MAPPING[key]

        # Save in Redis for background removal processing
        redis_data = { background_removal_key: background_removal_key, photo_id: photo.id }.to_json
        $redis.set(Constants.bg_remove_redis_key(key, @user.id), redis_data)
        $redis.expire(Constants.bg_remove_redis_key(key, @user.id), 60)
      end

      # Always store the processed photo data for comparison
      saved_photos[key] = {
        "type": key,
        "photo_id": photo.id.to_s
      }
    end

    unwanted_photo_types = existing_photo_types - new_photo_types
    unwanted_bg_removal_types = unwanted_photo_types.map do |type|
      User::POSTER_PHOTO_KEY_MAPPING[type]
    end
    # Handle deletion of unwanted photos
    UserMetadatum.where(user: @user, key: unwanted_photo_types).destroy_all
    UserMetadatum.where(user: @user, key: unwanted_bg_removal_types).destroy_all

    # Save family_frame_name if it's provided in params and the user has a family frame photo
    # or if a family frame photo is present in the current request
    if params[:family_frame_name].present? && params[:family_frame_name] != @user.family_frame_name &&
       (has_family_frame_photo || UserMetadatum.exists?(user: @user, key: Constants.family_frame_photo_original_key))
      @user.update(family_frame_name: params[:family_frame_name])
    elsif !has_family_frame_photo && @user.family_frame_name.present?
      @user.update(family_frame_name: nil)
    end

    # Return the photo data structure for comparison
    saved_photos
  end

  def trigger_remove_background_for_poster_photos
    # Loop through each key in the UserMetadatum
    UserMetadatum.where(user: @user, key: User::VALID_ORIGINAL_POSTER_PHOTO_KEYS).each do |um|
      # Get the background removal key and photo id from Redis
      redis_data = $redis.get(Constants.bg_remove_redis_key(um.key, @user.id))
      next if redis_data.blank?

      data = JSON.parse(redis_data)
      background_removal_key = data['background_removal_key']
      photo_id = data['photo_id']

      # Trigger the background removal worker
      MediaService::PosterPhotoBgRemovalProcessorWorker.perform_async(photo_id, background_removal_key)
    end
  end

  def process_and_save_protocol_leader_circles
    saved_circles = []
    h1_count = 0
    h2_count = 0

    poster_layout = @user.get_user_poster_layout_including_inactive ||
                    UserPosterLayout.new(entity: @user, active: false)

    protocol_leader_circles = params[:protocol_leader_circles].present? ? params[:protocol_leader_circles].values : {}

    # Build a lookup of existing records keyed by composite key: [header_type, priority, photo_id, circle_name]
    existing_records = poster_layout.user_leader_photos.index_by do |record|
      circle_id = record.circle_id
      circle_name = if circle_id.present?
                      Circle.find_by(id: circle_id)&.name_en
                    else
                      record.draft_data&.dig('circle_name')
                    end
      [
        record.header_type.to_s,
        record.priority.to_s,
        record.photo_id.to_s,
        circle_name.to_s
      ]
    end

    protocol_leader_circles.each do |circle_json|
      header_type = circle_json[:type].to_s
      priority = circle_json[:priority].to_s

      # Determine the selected photo id from poster_photos.
      poster_photos = circle_json[:poster_photos]
      photo_id = poster_photos&.values&.find { |photo| photo[:selected].to_s == "true" }&.dig(:id).to_s

      # Determine the circle name from incoming data.
      circle_name = if circle_json[:id].present?
                      Circle.find_by(id: circle_json[:id])&.name_en.to_s
                    else
                      circle_json[:name].to_s
                    end

      composite_key = [header_type, priority, photo_id, circle_name]

      if existing_records.key?(composite_key) && photo_id.present?
        # Found an existing record that matches exactly; keep it.
        existing_records.delete(composite_key)
      else
        leader_photo, draft_data = fetch_leader_photo_and_draft_data(circle_json, photo_id, circle_name)
        circle_id = circle_json[:id]
        raise ApiError.new(I18n.t('errors.protocol_leader_photo_blank'), :bad_request) if leader_photo.blank? && circle_id.present?
        poster_layout.user_leader_photos.build(
          photo: leader_photo,
          header_type: header_type,
          priority: priority,
          circle_id: circle_id,
          draft_data: draft_data,
          creator: current_creator
        ).save!
      end

      saved_circles << {
        "type": header_type,
        "priority": priority,
        "photo_id": photo_id,
        "circle_id": circle_json[:id],
        "circle_name": circle_name
      }

      h1_count += 1 if header_type == "header_1"
      h2_count += 1 if header_type == "header_2"
    end

    # Remove any remaining records that weren't present in the incoming data.
    existing_records.values.each(&:destroy)

    poster_layout.h1_count = h1_count
    poster_layout.h2_count = h2_count
    poster_layout.save!

    saved_circles
  end

  def fetch_leader_photo_and_draft_data(circle_json, photo_id, circle_name)
    leader_photo = nil
    draft_data = nil
    circle_id = circle_json[:id]

    if circle_id.blank?
      photo_file = circle_json[:photo_file]

      if photo_file.present?
        leader_photo = Photo.new(blob_data: photo_file, user: @user, service: :aws)
        # If the new photo fails to save, return nil so the caller can raise an error.
        raise ApiError.new(I18n.t('errors.leader_photo_save_error'), :bad_request) unless leader_photo.new_record? && leader_photo.save
      end

      raise ApiError.new(I18n.t('errors.requested_circle_name_blank'), :bad_request) if circle_name.blank?

      draft_data = { photo_id: leader_photo&.id || photo_id, circle_name: circle_name }.reject { |_, v| v.blank? }
    end

    leader_photo ||= Photo.find_by(id: photo_id) if photo_id.present?
    [leader_photo, draft_data]
  end

  def set_user
    user_id = params[:user_id]
    return render json: { message: I18n.t('errors.user_id_not_present') }, status: :bad_request if user_id.nil?

    @user = nil
    @user = User.find_by(id: user_id)

    render json: { message: I18n.t('errors.user_not_found') }, status: :not_found if @user.nil?
  end

  # Check if user has ever been in rm_submitted state
  def user_has_been_rm_submitted?
    UserMetadatum.exists?(user: @user, key: Constants.last_rm_submitted_data)
  end

  def fetch_selected_no_affiliated_party
    user_json = @user.user_json_for_rm_layout_creation_tool

    # If user has affiliated_party, always return false for selected_no_affiliated_party
    if user_json[:affiliated_party].present?
      return false
    end

    # Otherwise, use the existing logic
    selected_no_affiliated_party = UserMetadatum.find_by(user: @user,
                                                         key: Constants.selected_no_affiliated_party_key)&.value
    ActiveModel::Type::Boolean.new.cast(selected_no_affiliated_party)
  end
end
