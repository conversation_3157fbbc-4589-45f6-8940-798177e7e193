# frozen_string_literal: true
# Original file: app/workers/video_frame_templates/transparent_identity_and_transparent_protocol_over_video.rb
require 'rails_helper'

RSpec.describe VideoFrameTemplates::TransparentFlippedIdentityAndTransparentProtocolOverVideo do
  describe '.generate' do
    before do
      photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
      @user = FactoryBot.create(:user, name: "testing")
      @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
      @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
      @user_poster_layout =
        FactoryBot.create(:user_poster_layout, entity: @user,
                          h1_count: 1,
                          h2_count: 1,
                          user_leader_photos: [
                            FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                              header_type: :header_1, priority: 1),
                            FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                              header_type: :header_2, priority: 1)])
      allow(Capture).to receive(:capture_html_as_image).and_return({ 'cdn_url' => 'https://cdn_url.com' })
    end

    it 'creates a video frame with the correct attributes' do
      expect {
        described_class.generate(@user)
      }.to change { VideoFrame.count }.by(1)
      video_frame = VideoFrame.last
      expect(video_frame.frame_type).to eq('identity_and_protocol_over_video')
      expect(video_frame.supported_video_type).to eq('portrait')
      expect(video_frame.user_id).to eq(@user.id)
      expect(video_frame.data["name"]).to eq('transparent_flipped_identity_and_transparent_protocol_over_video')
      expect(video_frame.data["identity_photo_url"]).to be_present
      expect(video_frame.data["protocol_photo_url"]).to be_present
    end
  end
end
