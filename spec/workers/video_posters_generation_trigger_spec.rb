require 'rails_helper'

RSpec.describe VideoPostersGenerationTrigger, type: :worker do
  before do
    @user = FactoryBot.create(:user)
  end

  describe '#perform' do
    context 'when triggered for pending video poster' do
      let(:mock_lambda_client) { instance_double(Aws::Lambda::Client) }
      it 'transitions the video poster to processing state' do
        video_frame = FactoryBot.create(:video_frame, user_id: @user.id)
        video = FactoryBot.create(:video, user_id: @user.id)
        video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, video: video, user_id: @user.id, source_video_id: video.id)
        lambda_payload =
          {
            video_url: video.source_url,
            layout_data: video_frame.data,
            layout_type: video_frame.frame_type,
            job_id: video_poster.job_id,
            callback_url: "#{Constants.get_api_host}/video-posters",
          }
        allow(Aws::Lambda::Client).to receive(:new).and_return(mock_lambda_client)
        allow(mock_lambda_client).to receive(:invoke).and_return(nil)
        VideoPostersGenerationTrigger.new.perform(lambda_payload, video_poster.id, video_poster.job_id)
        video_poster.reload
        expect(video_poster.status.to_sym).to eq(:processing)
      end
    end
  end
end
