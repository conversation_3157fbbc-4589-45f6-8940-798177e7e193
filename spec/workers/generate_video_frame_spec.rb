# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GenerateVideoFrames, type: :worker do
  describe '#perform' do
    let(:user) { FactoryBot.create(:user) }

    before do
      allow(User).to receive(:find).and_return(user)
      allow(VideoFrame).to receive_message_chain(:where, :update_all)
      allow(VideoFrameTemplates::BlackProtocolAndWhiteIdentity).to receive(:generate)
      allow(VideoFrameTemplates::BlackProtocolAndFlippedWhiteIdentity).to receive(:generate)
      allow(VideoFrameTemplates::VerticalProtocolHorizontalIdentityPortrait).to receive(:generate)
      allow(VideoFrameTemplates::FlippedVerticalProtocolHorizontalIdentityPortrait).to receive(:generate)
      allow(VideoFrameTemplates::TransparentIdentityOverVideo).to receive(:generate)
      allow(VideoFrameTemplates::TransparentIdentityAndTransparentProtocolOverVideo).to receive(:generate)
      allow(VideoFrameTemplates::TransparentFlippedIdentityOverVideo).to receive(:generate)
      allow(VideoFrameTemplates::TransparentFlippedIdentityAndTransparentProtocolOverVideo).to receive(:generate)
    end

    it 'finds the user' do
      described_class.new.perform(user.id)
      expect(User).to have_received(:find).with(user.id)
    end

    it 'updates existing video frames to inactive' do
      described_class.new.perform(user.id)
      expect(VideoFrame.where(user_id: user.id)).to have_received(:update_all).with(active: false)
    end

    it 'calls generate on BlackProtocolAndWhiteIdentity template' do
      described_class.new.perform(user.id)
      expect(VideoFrameTemplates::BlackProtocolAndWhiteIdentity).to have_received(:generate).with(user)
    end

    it 'calls generate on BlackProtocolAndFlippedWhiteIdentity template' do
      described_class.new.perform(user.id)
      expect(VideoFrameTemplates::BlackProtocolAndFlippedWhiteIdentity).to have_received(:generate).with(user)
    end

    it 'calls generate on VerticalProtocolHorizontalIdentityPortrait template' do
      described_class.new.perform(user.id)
      expect(VideoFrameTemplates::VerticalProtocolHorizontalIdentityPortrait).to have_received(:generate).with(user)
    end

    it 'calls generate on IdentityOverVideo template' do
      described_class.new.perform(user.id)
      expect(VideoFrameTemplates::TransparentIdentityOverVideo).to have_received(:generate).with(user)
    end

    it 'calls generate on IdentityAndProtocolOverVideo template' do
      described_class.new.perform(user.id)
      expect(VideoFrameTemplates::TransparentIdentityAndTransparentProtocolOverVideo).to have_received(:generate).with(user)
    end

    it 'calls generate on FlippedIdentityOverVideo template' do
      described_class.new.perform(user.id)
      expect(VideoFrameTemplates::TransparentFlippedIdentityOverVideo).to have_received(:generate).with(user)
    end

    it 'calls generate on FlippedIdentityAndProtocolOverVideo template' do
      described_class.new.perform(user.id)
      expect(VideoFrameTemplates::TransparentFlippedIdentityAndTransparentProtocolOverVideo).to have_received(:generate).with(user)
    end

    it 'calls generate on FlippedVerticalProtocolHorizontalIdentityPortrait template' do
      described_class.new.perform(user.id)
      expect(VideoFrameTemplates::FlippedVerticalProtocolHorizontalIdentityPortrait).to have_received(:generate).with(user)
    end
  end
end
