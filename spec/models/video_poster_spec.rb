# spec/models/video_poster_spec.rb

require 'rails_helper'

RSpec.describe VideoPoster, type: :model do
  before :all do
    stub_request(:put, Constants.get_dm_url + '/video-posters/status-updated')
      .to_return(status: 200, body: '', headers: {})
  end

  user = FactoryBot.create(:user)
  video = FactoryBot.create(:video, user_id: user.id)
  describe 'AASM transitions' do
    context 'when in pending state' do
      it 'transitions to processing on process event' do
        video_frame = FactoryBot.create(:video_frame, user_id: user.id)
        video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, user_id: user.id, source_video_id: video.id)
        expect(video_poster).to transition_from(:pending).to(:processing).on_event(:process)
      end
    end

    context 'when in processing state' do

      it 'transitions to failed on fail event' do
        video_frame = FactoryBot.create(:video_frame, user_id: user.id)
        video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, user_id: user.id, source_video_id: video.id)
        expect(video_poster).to transition_from(:processing).to(:failed).on_event(:fail)
      end
    end

    context 'when in failed state' do
      it 'transitions to pending on mark_as_pending event' do
        video_frame = FactoryBot.create(:video_frame, user_id: user.id)
        video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, user_id: user.id, source_video_id: video.id, status: :failed)
        expect(video_poster).to transition_from(:failed).to(:pending).on_event(:mark_as_pending)
      end
    end
  end

  describe '#generation_failed' do
    it 'sets the error_code' do
      video_frame = FactoryBot.create(:video_frame, user_id: user.id)
      video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, user_id: user.id, source_video_id: video.id)
      expect {
        video_poster.generation_failed('error_123')
      }.to change { Notification.count }.by(0)
      expect(video_poster.error_code).to eq('error_123')
    end
  end

  describe '#save_generated_video_object' do
    it 'builds a video object if video_id is nil' do
      video_frame = FactoryBot.create(:video_frame, user_id: user.id)
      video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, user_id: user.id, source_video_id: video.id)

      video_poster.video_id = nil
      video_poster.save_generated_video_object
      expect(video_poster.video).to be_present
      expect(video_poster.video.url).to eq("https://ruv-cdn.thecircleapp.in/video-posters/#{video_poster.job_id}-video.mp4")
    end
  end


  describe 'Notification creation' do
    let(:video_frame) { FactoryBot.create(:video_frame, user_id: user.id) }
    let(:video_poster) { FactoryBot.create(:video_poster, video_frame: video_frame, user_id: user.id, source_video_id: video.id) }

    context 'when video poster is completed' do
      it 'creates a notification' do
        video_poster.processing!
        expect {
          video_poster.complete!
        }.to change { Notification.count }.by(1)
      end
    end

    context 'when video poster has failed' do
      it 'creates a notification' do
        video_poster.processing!
        expect {
          video_poster.fail!('error_123')
        }.to change { Notification.count }.by(0)
      end
    end
  end
end
