require 'rails_helper'

RSpec.describe Subscription, type: :model do
  describe '#get_open_subscription' do
    let(:user) { create(:user) }
    let(:plan) { create(:plan) }

    context 'when there is an open subscription' do
      let!(:subscription) { create(:subscription, plan: plan, user: user, status: :created) }

      it 'returns the open subscription' do
        expect(Subscription.get_open_subscription(plan.id, user.id)).to eq(subscription)
      end
    end

    context 'when there is no open subscription' do
      it 'returns nil' do
        expect(Subscription.get_open_subscription(plan.id, user.id)).to be_nil
      end
    end
  end

  describe '#downgrade_consent' do
    let(:subscription) { create(:subscription) }

    it 'creates a metadatum with downgrade consent' do
      subscription.mark_subscription_to_take_consent_for_downgrade
      expect(subscription.metadatum.where(key: Constants.plan_downgrade_consent_key)).to exist
    end

    it 'returns true if downgrade consent is present' do
      subscription.mark_subscription_to_take_consent_for_downgrade
      expect(subscription.need_to_get_consent_for_downgrade?).to be_truthy
    end

    it 'returns false if downgrade consent is not present' do
      expect(subscription.need_to_get_consent_for_downgrade?).to be_falsey
    end

    it 'should mark the subscription plan downgrade consent as yes' do
      subscription.mark_subscription_to_take_consent_for_downgrade
      subscription.mark_subscription_plan_downgrade_consent_as_yes
      expect(subscription.metadatum.where(key: Constants.plan_downgrade_consent_key).first.value).to eq('yes')
    end

    it 'should mark the subscription plan downgrade consent as no' do
      subscription.mark_subscription_to_take_consent_for_downgrade
      subscription.mark_subscription_plan_downgrade_consent_as_no
      expect(subscription.metadatum.where(key: Constants.plan_downgrade_consent_key).first.value).to eq('no')
    end

    it 'should mark the subscription plan downgrade consent as expired when next_retry_date is nil' do
      subscription.mark_subscription_to_take_consent_for_downgrade

      # Create a user plan for the subscription's user
      create(:user_plan, user: subscription.user, plan: subscription.plan, end_date: Time.zone.now.end_of_day)

      # Mock the evaluate_and_apply_grace_period method to avoid issues with grace period
      allow(subscription).to receive(:evaluate_and_apply_grace_period)

      # Mock the get_next_retry_date method to test the specific code path
      allow(subscription).to receive(:get_next_retry_date).and_return(nil)

      # Create a failed subscription charge
      failed_charge = FactoryBot.build(:subscription_charge, subscription: subscription, status: :failed)

      # Call the method that would normally be called by after_fail in SubscriptionCharge
      subscription.handle_charge_payment_failure(last_failed_subscription_charge: failed_charge)

      expect(subscription.metadatum.where(key: Constants.plan_downgrade_consent_key).first.value).to eq('expired')
    end
  end

  describe '#after_activate for downgrade consent' do
    it 'should mark the subscription plan downgrade consent as not needed as subscription got activated' do
      subscription = create(:subscription)
      subscription.mark_subscription_to_take_consent_for_downgrade
      subscription.status = :created
      SubscriptionCharge.create!(subscription: subscription, status: :success, amount: 1, user: subscription.user, charge_date: Time.zone.now)
      subscription.save!
      subscription.activate!
      expect(subscription.metadatum.where(key: Constants.plan_downgrade_consent_key).first.value).to eq('not_needed')
    end
  end

  describe '#get_open_juspay_subscription' do
    let(:user) { create(:user) }
    let(:plan) { create(:plan) }

    context 'when there is an open subscription' do
      let!(:subscription) { create(:subscription, plan: plan, user: user, status: :created, payment_gateway: :juspay) }

      it 'returns the open juspay subscription' do
        expect(Subscription.get_open_juspay_subscription(plan.id, user.id)).to eq(subscription)
      end
    end

    context 'when there is no open juspay subscription' do
      it 'returns nil' do
        expect(Subscription.get_open_juspay_subscription(plan.id, user.id)).to be_nil
      end
    end
  end

  describe '#create_plan_subscription' do
    let(:user) { create(:user, email: '<EMAIL>') }
    let(:plan) { create(:plan, amount: 100) }

    before do
      allow(CashfreePaymentUtils).to receive(:cashfree_post_v1).and_return('data' => { 'subReferenceId' => 'sub_ref_123', 'authLink' => 'auth_link_123' })
    end

    context 'when user is eligible for start trial' do
      before do
        allow(user).to receive(:is_eligible_for_start_trial?).and_return(true)
      end

      it 'creates a subscription with  initial charge of 1' do
        subscription = Subscription.create_plan_subscription(plan: plan, user: user)
        expect(subscription.initial_charge).to eq(1)
      end
    end

    context 'when user is not eligible for start trial' do
      before do
        allow(user).to receive(:is_eligible_for_start_trial?).and_return(false)
      end

      it 'creates a subscription with initial charge equal to plan amount' do
        subscription = Subscription.create_plan_subscription(plan: plan, user: user)
        expect(subscription.initial_charge).to eq(plan.amount)
      end
    end
  end

  describe '#create_plan_juspay_subscription' do
    let(:user) { create(:user, email: '<EMAIL>') }
    let(:plan) { create(:plan, amount: 100) }

    before do
      allow(JuspayPaymentUtils).to receive(:generate_pg_id).and_return('pg_id_123')
      allow(JuspayPaymentUtils).to receive(:post).and_return('payment_links' => { 'mobile' => 'mobile_link' })
    end

    context 'when user is eligible for start trial' do
      before do
        allow(user).to receive(:is_eligible_for_start_trial?).and_return(true)
        allow(user).to receive(:get_juspay_customer_id).and_return('asdfsf')
      end

      it 'creates a subscription with initial charge of 1' do
        subscription = Subscription.create_plan_juspay_subscription(plan: plan, user: user)
        expect(subscription.initial_charge).to eq(1)
        expect(subscription.pg_id).to eq('pg_id_123')
      end
    end

    context 'when user is not eligible for start trial' do
      before do
        allow(user).to receive(:is_eligible_for_start_trial?).and_return(false)
        allow(user).to receive(:get_juspay_customer_id).and_return('asdfsf')
      end

      it 'creates a subscription with initial charge equal to plan amount' do
        subscription = Subscription.create_plan_juspay_subscription(plan: plan, user: user)
        expect(subscription.initial_charge).to eq(plan.amount)
      end
    end
  end

  describe '#cancel' do
    let(:subscription) { create(:subscription) }

    it 'creates a metadatum with cancellation reason and cancels the subscription' do
      expect(subscription).to receive(:cancel!)
      subscription.cancel('User requested')
      expect(subscription.metadatum.last.key).to eq('cancellation_reason')
      expect(subscription.metadatum.last.value).to eq('User requested')
    end
  end

  describe '#after_cancel' do
    context 'when subscription pg is cashfree' do
      let(:subscription) { create(:subscription, pg_reference_id: 'pg_ref_123') }

      before do
        allow(CashfreePaymentUtils).to receive(:cashfree_post_v1)
        @subscription_charge = FactoryBot.create(:subscription_charge, status: :success, subscription: subscription)
      end

      it 'calls the cashfree API to cancel the subscription' do
        subscription.send(:after_cancel)
        expect(CashfreePaymentUtils).to have_received(:cashfree_post_v1)
                                          .with("/subscriptions/#{subscription.pg_reference_id}/cancel", {})
      end
    end

    context 'when subscription pg is juspay' do
      it 'calls the Juspay API to cancel the subscription' do
        pg_id = JuspayPaymentUtils.generate_pg_id
        subscription = FactoryBot.create(:subscription,
                                         pg_reference_id: 'pg_ref_123',
                                         payment_gateway: :juspay)

        allow(JuspayPaymentUtils).to receive(:post)
        FactoryBot.create(:subscription_charge, status: :success, subscription: subscription)
        subscription.send(:after_cancel)
        expect(JuspayPaymentUtils).to have_received(:post)
                                        .with("/mandates/#{subscription.pg_reference_id}", { command: 'revoke' })
      end
    end
  end

  describe '#after_customer_cancel' do
    context 'when payment_gateway is cashfree' do
      let(:subscription) { create(:subscription, pg_reference_id: 'pg_ref_1234') }

      before do
        allow(EventTracker).to receive(:perform_async)
      end

      it 'sends a cancelled mixpanel event' do
        subscription.send(:after_customer_cancel)
        expect(EventTracker).to have_received(:perform_async)
                                          .with(subscription.user_id,
                                                "subscription_cancelled_backend",
                                                hash_including("plan_id" => anything,
                                                               "amount" => anything,
                                                               "subscription_id" => anything,
                                                               "reason" => anything,
                                                               "source" => anything,
                                                               "subscription_activated_date" => anything))
      end
    end

    context 'when payment_gateway is juspay' do
      let(:subscription) { create(:subscription, payment_gateway: :juspay) }

      before do
        allow(EventTracker).to receive(:perform_async)
      end

      it 'sends a cancelled mixpanel event' do
        subscription.send(:after_customer_cancel)
        expect(EventTracker).to have_received(:perform_async)
                                          .with(subscription.user_id,
                                                "subscription_cancelled_backend",
                                                hash_including("plan_id" => anything,
                                                               "amount" => anything,
                                                               "subscription_id" => anything,
                                                               "reason" => anything,
                                                               "source" => anything,
                                                               "subscription_activated_date" => anything))
      end
    end
  end

  describe '#after_pause' do
    let(:subscription) { create(:subscription) }

    before do
      allow(EventTracker).to receive(:perform_async)
    end

    it 'sends a paused mixpanel event' do
      subscription.send(:after_pause)
      expect(EventTracker).to have_received(:perform_async)
                                        .with(subscription.user_id,
                                              "subscription_paused_backend",
                                              hash_including("plan_id" => anything,
                                                             "amount" => anything,
                                                             "source" => anything,
                                                             "subscription_id" => anything))
    end
  end

  describe '#after_customer_paused' do
    context 'when subscription pg is cashfree' do
      let(:subscription) { create(:subscription) }

      before do
        allow(CashfreePaymentUtils).to receive(:cashfree_post_v1)
        allow(EventTracker).to receive(:perform_async)
      end

      it 'calls the Cashfree API to cancel the subscription & sends mixpanel event' do
        subscription.send(:after_customer_paused)
        expect(CashfreePaymentUtils).to have_received(:cashfree_post_v1)
                                          .with("/subscriptions/#{subscription.pg_reference_id}/cancel", {})
        expect(EventTracker).to have_received(:perform_async)
                                          .with(subscription.user_id,
                                                "subscription_cancelled_backend",
                                                hash_including("plan_id" => anything,
                                                               "amount" => anything,
                                                               "subscription_id" => anything,
                                                               "reason" => anything,
                                                               "source" => anything,
                                                               "subscription_activated_date" => anything))
      end
    end

    context 'when subscription pg is juspay' do
      let(:subscription) { create(:subscription, payment_gateway: :juspay) }

      before do
        allow(JuspayPaymentUtils).to receive(:post)
        allow(EventTracker).to receive(:perform_async)
      end

      it 'calls the Juspay API to cancel the subscription & sends mixpanel event' do
        subscription.send(:after_customer_paused)
        expect(JuspayPaymentUtils).to have_received(:post)
                                        .with("/mandates/#{subscription.pg_reference_id}", { command: 'revoke' })
        expect(EventTracker).to have_received(:perform_async)
                                          .with(subscription.user_id,
                                                "subscription_cancelled_backend",
                                                hash_including("plan_id" => anything,
                                                               "amount" => anything,
                                                               "subscription_id" => anything,
                                                               "reason" => anything,
                                                               "source" => anything,
                                                               "subscription_activated_date" => anything))
      end
    end
  end

  describe '#after_activate' do
    let(:monthly_plan) { create(:plan, duration_in_months: 1) }
    let(:yearly_plan) { create(:plan, duration_in_months: 12) }
    let(:user) { create(:user) }
    let(:previous_subscription) { create(:subscription, user: user, plan: monthly_plan) }
    let(:subscription) { create(:subscription, user: user, plan: yearly_plan) }

    before do
      allow(subscription.user).to receive(:delete_yet_to_start_trial_key)
      allow(EventTracker).to receive(:perform_async)
      allow(CancelUserOtherSubscriptions).to receive(:perform_async)
    end

    it 'deletes the yet to start trial key and sends an activated mixpanel event' do
      FactoryBot.create(:subscription_charge, status: :success, subscription: subscription)
      subscription.send(:after_activate)
      expect(subscription.user).to have_received(:delete_yet_to_start_trial_key)
      expect(EventTracker).to have_received(:perform_async)
                                        .with(subscription.user_id,
                                              "subscription_activated_backend",
                                              hash_including("duration_in_months" => anything,
                                                             "plan_id" => anything,
                                                             "amount" => anything,
                                                             "subscription_id" => anything,
                                                             "initial_charge" => anything,
                                                             "payment_gateway" => anything))
      expect(CancelUserOtherSubscriptions).to have_received(:perform_async).with(subscription.id)
    end

    it 'send upgrade as true in analytics params if user subscribed to yearly from monthly' do
      FactoryBot.create(:subscription_charge, status: :success, subscription: previous_subscription, user: user,
                        charge_date: Time.zone.now.advance(months: -1))
      FactoryBot.create(:subscription_charge, status: :success, subscription: subscription, user: user)
      subscription.send(:after_activate)
      expect(EventTracker).to have_received(:perform_async)
                                        .with(subscription.user_id,
                                              "subscription_activated_backend",
                                              hash_including("duration_in_months" => anything,
                                                             "plan_id" => anything,
                                                             "amount" => anything,
                                                             "subscription_id" => anything,
                                                             "initial_charge" => anything,
                                                             "payment_gateway" => anything,
                                                             "is_upgrade" => true,
                                                             "is_trial_setup_charge" => anything))
    end

    it 'send upgrade as false in analytics params if user subscribed from monthly to monthly again' do
      subscription = create(:subscription, user: user, plan: monthly_plan)
      FactoryBot.create(:subscription_charge, status: :success, subscription: previous_subscription,
                        user: user, charge_date: Time.zone.now.advance(months: -1))
      FactoryBot.create(:subscription_charge, status: :success, subscription: subscription, user: user)
      subscription.send(:after_activate)
      expect(EventTracker).to have_received(:perform_async)
                                        .with(subscription.user_id,
                                              "subscription_activated_backend",
                                              hash_including("duration_in_months" => anything,
                                                             "plan_id" => anything,
                                                             "amount" => anything,
                                                             "subscription_id" => anything,
                                                             "initial_charge" => anything,
                                                             "payment_gateway" => anything,
                                                             "is_upgrade" => false))
    end

    it 'send is_trial_setup_charge as true if it is first charge which is 1 rupee' do
      user = FactoryBot.create(:user)
      FactoryBot.create(:subscription_charge, status: :success, subscription: subscription, user: user, charge_amount: 1, amount: 1)
      subscription.send(:after_activate)
      expect(EventTracker).to have_received(:perform_async)
                                        .with(subscription.user_id,
                                              "subscription_activated_backend",
                                              hash_including("duration_in_months" => anything,
                                                             "plan_id" => anything,
                                                             "amount" => anything,
                                                             "subscription_id" => anything,
                                                             "initial_charge" => anything,
                                                             "payment_gateway" => anything,
                                                             "is_trial_setup_charge" => true))
    end

    it 'send is_trial_setup_charge as false if it is not first charge which is 1 rupee' do
      FactoryBot.create(:subscription_charge, status: :success, subscription: subscription, user: user,
                        charge_amount: 100, amount: 100)
      subscription.send(:after_activate)
      expect(EventTracker).to have_received(:perform_async)
                                        .with(subscription.user_id,
                                              "subscription_activated_backend",
                                              hash_including("duration_in_months" => anything,
                                                             "plan_id" => anything,
                                                             "amount" => anything,
                                                             "subscription_id" => anything,
                                                             "initial_charge" => anything,
                                                             "payment_gateway" => anything,
                                                             "is_trial_setup_charge" => false))
    end

  end

  describe '#remove_upgrade_package_sheet_key' do
    let(:user) { create(:user) }
    let(:subscription) { create(:subscription, user: user) }

    context 'when user has an upgrade package sheet key' do
      before do
        UserMetadatum.create(user: user, key: Constants.upgrade_package_sheet_key, value: 'to_be_shown')
      end

      it 'removes the upgrade package sheet key' do
        expect { subscription.remove_upgrade_package_sheet_key }
          .to change { UserMetadatum.where(key: Constants.upgrade_package_sheet_key).count }
                .from(1).to(0)
      end
    end

    context 'when user does not have an upgrade package sheet key' do
      it 'does not change the metadatum count' do
        expect { subscription.remove_upgrade_package_sheet_key }
          .not_to change { UserMetadatum.count }
      end
    end
  end

  describe "#get_next_retry_date" do
    let(:user) { create(:user) }
    let(:plan) { create(:plan) }
    let(:subscription) { create(:subscription, max_amount: plan.amount, user: user, initial_charge: 1) }
    let(:initial_charge) { create(:subscription_charge, subscription: subscription,
                                  user: user, charge_amount: subscription.initial_charge, amount: subscription.initial_charge, status: :success,
                                  charge_date: Time.zone.local(2024, 1, 14, 0, 0, 0),
                                  created_at: Time.zone.local(2024, 1, 14, 0, 0, 0)) }
    let(:user_plan) { create(:user_plan, user: user, plan: plan, amount: plan.amount,
                             end_date: Time.zone.now.tomorrow.end_of_day) }

    context "when it is the 1st attempt and charge is failed at 2nd of the month" do
      it "returns the next retry date as 4 as next eligible retry day will be the 4t of the month after 2nd" do
        subscription
        initial_charge
        travel_to Time.zone.local(2024, 2, 3, 14, 0, 0)
        first_charge = FactoryBot.create(:subscription_charge, subscription: subscription,
                                         user: user, charge_amount: subscription.max_amount, amount: subscription.max_amount, status: :failed,
                                         charge_date: Time.zone.local(2024, 2, 3, 0, 0, 0),
                                         created_at: Time.zone.local(2024, 2, 2, 0, 0, 0))

        user.metadatum.create(key: Constants.grace_period_given_string, value: (user_plan.end_date + 1.days).beginning_of_day)

        expect(subscription.get_next_retry_date(current_charge_date: first_charge.charge_date)).to eq(Time.zone.local(2024, 2, 6, 0, 0, 0))
      end
    end

    context "when it is the 1st attempt and charge is failed at 30 th of the month" do
      it "returns the next retry date as 2 as next eligible retry day will be the 2nd of the next month after 30th" do
        subscription
        initial_charge
        travel_to Time.zone.local(2024, 1, 31, 19, 0, 0)
        first_charge = FactoryBot.create(:subscription_charge, subscription: subscription,
                                         user: user, charge_amount: subscription.max_amount, amount: subscription.max_amount, status: :failed,
                                         charge_date: Time.zone.local(2024, 1, 30, 0, 0, 0),
                                         created_at: Time.zone.local(2024, 1, 30, 0, 0, 0))

        user.metadatum.create(key: Constants.grace_period_given_string, value: (user_plan.end_date + 1.days).beginning_of_day)

        expect(subscription.get_next_retry_date(current_charge_date: first_charge.charge_date)).to eq(Time.zone.local(2024, 2, 2, 0, 0, 0))
      end
    end

    context "when it is the 1st attempt and charge is failed at 1st th of the next month which should get call back on on 30 th of last month and callback arrival time is after 3 PM" do
      it "returns the next retry date as 4 as next eligible retry day will be the 2nd of the next month after callback arriaval on 1 st of month" do
        subscription
        initial_charge
        travel_to Time.zone.local(2024, 2, 1, 19, 0, 0)

        first_charge = FactoryBot.create(:subscription_charge, subscription: subscription,
                                         user: user, charge_amount: subscription.max_amount, amount: subscription.max_amount, status: :failed,
                                         charge_date: Time.zone.local(2024, 1, 30, 0, 0, 0),
                                         created_at: Time.zone.local(2024, 1, 30, 0, 0, 0))

        user.metadatum.create(key: Constants.grace_period_given_string, value: (user_plan.end_date + 1.days).beginning_of_day)

        expect(subscription.get_next_retry_date(current_charge_date: first_charge.charge_date)).to eq(Time.zone.local(2024, 2, 4, 0, 0, 0))
      end
    end

    context "when it is the 1st attempt and charge is failed at 1st th of the next month which should get call back on on 30 th of last month and callback arrival time is before 3 PM" do
      it "returns the next retry date as 2 as next eligible retry day will be the 2nd of the next month after callback arriaval on 1 st of month" do
        subscription
        initial_charge
        travel_to Time.zone.local(2024, 2, 1, 10, 0, 0)
        first_charge = FactoryBot.create(:subscription_charge, subscription: subscription,
                                         user: user, charge_amount: subscription.max_amount, amount: subscription.max_amount, status: :failed,
                                         charge_date: Time.zone.local(2024, 1, 30, 0, 0, 0),
                                         created_at: Time.zone.local(2024, 1, 30, 0, 0, 0))

        user.metadatum.create(key: Constants.grace_period_given_string, value: (user_plan.end_date + 1.days).beginning_of_day)

        expect(subscription.get_next_retry_date(current_charge_date: first_charge.charge_date)).to eq(Time.zone.local(2024, 2, 2, 0, 0, 0))
      end
    end

    context "when it is the 1st attempt and charge is failed at 3rd th of the next month which should get call back on on 30 th of last month and callback arrival time is before 3 PM" do
      it "returns the next retry date as 4 as next eligible retry day will be the 2nd of the next month after callback arriaval on 1 st of month" do
        subscription
        initial_charge
        travel_to Time.zone.local(2024, 2, 3, 10, 0, 0)
        first_charge = FactoryBot.create(:subscription_charge, subscription: subscription,
                                         user: user, charge_amount: subscription.max_amount, amount: subscription.max_amount, status: :failed,
                                         charge_date: Time.zone.local(2024, 1, 30, 0, 0, 0),
                                         created_at: Time.zone.local(2024, 1, 30, 0, 0, 0))

        user.metadatum.create(key: Constants.grace_period_given_string, value: (user_plan.end_date + 1.days).beginning_of_day)

        expect(subscription.get_next_retry_date(current_charge_date: first_charge.charge_date)).to eq(Time.zone.local(2024, 2, 4, 0, 0, 0))
      end
    end

    context "when it is the 1st attempt and charge is failed at 2nd th of the next month which should get call back on on 30 th of last month and callback arrival time is after 3 PM" do
      it "returns the next retry date as 6 as next eligible retry day will be the 2nd of the next month after callback arriaval on 1 st of month" do
        subscription
        initial_charge
        travel_to Time.zone.local(2024, 2, 3, 17, 0, 0)
        first_charge = FactoryBot.create(:subscription_charge, subscription: subscription,
                                         user: user, charge_amount: subscription.max_amount, amount: subscription.max_amount, status: :failed,
                                         charge_date: Time.zone.local(2024, 1, 30, 0, 0, 0),
                                         created_at: Time.zone.local(2024, 1, 30, 0, 0, 0))

        user.metadatum.create(key: Constants.grace_period_given_string, value: (user_plan.end_date + 1.days).beginning_of_day)

        expect(subscription.get_next_retry_date(current_charge_date: first_charge.charge_date)).to eq(Time.zone.local(2024, 2, 6, 0, 0, 0))
      end
    end

  end

  describe '#get_downgradable_date' do
    context 'should return the down gradable date based on the attempt number assigned for ' do
      it 'should return the down gradable date based on the attempt number assigned for ' do
        travel_to(Time.parse("Fri, 21 Nov 2024 00:00:00.********* IST +05:30"))
        user = FactoryBot.create(:user)
        subscription = FactoryBot.create(:subscription, user: user)
        plan = FactoryBot.create(:plan)
        FactoryBot.create(:user_plan, user: user, plan: plan, end_date: Time.zone.parse("2024-11-21"))
        FactoryBot.create(:metadatum, entity_id: user.id, entity_type: 'User', key: Constants.grace_period_given_string, value: Time.zone.parse("2024-11-22"))
        FactoryBot.create(:subscription_charge, status: :failed, subscription: subscription, charge_date: Time.zone.parse("2024-11-22"), attempt_number: 1, payment_id: ULID.generate, charge_amount: subscription.max_amount, amount: subscription.max_amount)
        down_grade_date = subscription.get_down_gradable_date
        expect(down_grade_date).to eq(Time.zone.parse("2024-12-13"))
      end
    end
  end

  describe '#get_nth_retry_charge_date' do
    context 'should return the nth retry date based on the charge date given ' do
      it 'should return the nth retry date after the given charge date ' do
        travel_to(Time.parse("Fri, 21 Nov 2024 00:00:00.********* IST +05:30"))
        user = FactoryBot.create(:user)
        subscription = FactoryBot.create(:subscription, user: user)
        plan = FactoryBot.create(:plan)
        FactoryBot.create(:user_plan, user: user, plan: plan, end_date: Time.zone.parse("2024-11-21"))
        FactoryBot.create(:metadatum, entity_id: user.id, entity_type: 'User', key: Constants.grace_period_given_string, value: Time.zone.parse("2024-11-22"))
        sc = FactoryBot.create(:subscription_charge, status: :failed, subscription: subscription, charge_date: Time.zone.parse("2024-11-22"), attempt_number: 1, payment_id: ULID.generate)
        expect(subscription.get_nth_retry_charge_date(current_charge_date: sc.charge_date, n: 10)).to eq(Time.zone.parse("2024-12-15"))
      end

      it 'should return the nth retry date after the given charge date ' do
        travel_to(Time.parse("Wed, 02 Apr 2025 00:00:00.********* IST +05:30"))
        user = FactoryBot.create(:user)
        subscription = FactoryBot.create(:subscription, user: user)
        plan = FactoryBot.create(:plan)
        FactoryBot.create(:user_plan, user: user, plan: plan, end_date: Time.zone.parse("Thu, 20 Mar 2025 23:59:59.********* IST +05:30"))
        FactoryBot.create(:metadatum, entity_id: user.id, entity_type: 'User', key: Constants.grace_period_given_string, value: Time.zone.parse("Thu, 21 Mar 2025 23:59:59.********* IST +05:30"))
        sc = FactoryBot.create(:subscription_charge, status: :failed, subscription: subscription, charge_date: Time.zone.parse("Thu, 20 Mar 2025 23:59:59.********* IST +05:30"), attempt_number: 1, payment_id: ULID.generate)
        expect(subscription.get_nth_retry_charge_date(current_charge_date: sc.charge_date, n: 9)).to eq(Time.zone.parse("Sun, 13 Apr 2025 00:00:00.********* IST +05:30"))
      end

      it 'should return the nil as date after the given charge date  because he is not in grace period' do
        travel_to(Time.parse("Fri, 21 Nov 2024 00:00:00.********* IST +05:30"))
        user = FactoryBot.create(:user)
        subscription = FactoryBot.create(:subscription, user: user)
        plan = FactoryBot.create(:plan)
        FactoryBot.create(:user_plan, user: user, plan: plan, end_date: Time.zone.parse("2024-11-21"))
        # FactoryBot.create(:metadatum, entity_id: user.id, entity_type: 'User', key: Constants.grace_period_given_string, value: Time.zone.parse("2024-11-22"))
        sc = FactoryBot.create(:subscription_charge, status: :failed, subscription: subscription, charge_date: Time.zone.parse("2024-11-22"), attempt_number: 1, payment_id: ULID.generate)
        expect(subscription.get_nth_retry_charge_date(current_charge_date: sc.charge_date, n: 12)).to eq(nil)
      end
    end
  end

  describe '#get_days_left_in_grace_period' do
    context 'should return the number of days left in the grace period' do
      it 'should return the 23 days left in the grace period  ' do
        travel_to(Time.parse("Fri, 28 Nov 2024 00:00:00.********* IST +05:30"))
        user = FactoryBot.create(:user)
        subscription = FactoryBot.create(:subscription, user: user)
        plan = FactoryBot.create(:plan)
        FactoryBot.create(:user_plan, user: user, plan: plan, end_date: Time.zone.parse("2024-11-21"))
        FactoryBot.create(:metadatum, entity_id: user.id, entity_type: 'User', key: Constants.grace_period_given_string, value: Time.zone.parse("2024-11-22"))
        sc = FactoryBot.create(:subscription_charge, status: :failed, subscription: subscription, charge_date: Time.zone.parse("2024-11-22"), attempt_number: 1, payment_id: ULID.generate)

        expect(subscription.get_days_left_in_grace_period).to eq(22)
      end
      it 'should return 0 as grace period is already completed' do
        travel_to(Time.parse("Fri, 21 DEC 2024 00:00:00.********* IST +05:30"))
        user = FactoryBot.create(:user)
        subscription = FactoryBot.create(:subscription, user: user)
        plan = FactoryBot.create(:plan)
        FactoryBot.create(:user_plan, user: user, plan: plan, end_date: Time.zone.parse("2024-11-21"))
        FactoryBot.create(:metadatum, entity_id: user.id, entity_type: 'User', key: Constants.grace_period_given_string, value: Time.zone.parse("2024-11-22"))
        sc = FactoryBot.create(:subscription_charge, status: :failed, subscription: subscription, charge_date: Time.zone.parse("2024-11-22"), attempt_number: 1, payment_id: ULID.generate)
        expect(subscription.get_days_left_in_grace_period).to eq(0)
      end
    end
  end

end
