# spec/models/video_frame_spec.rb
require 'rails_helper'

RSpec.describe VideoFrame, type: :model do
  describe 'validations' do

    context 'when frame_type is video_between_protocol_and_identity' do
      it 'validates presence of data' do
        video_frame = VideoFrame.new(frame_type: :video_between_protocol_and_identity, data: { 'protocol_photo_url' => 'url', 'identity_photo_url' => 'url' })
        expect(video_frame).to be_valid
      end

      it 'is invalid without protocol_photo_url in data' do
        video_frame = VideoFrame.new(frame_type: :video_between_protocol_and_identity, data: { 'identity_photo_url' => 'url' })
        expect(video_frame).not_to be_valid
      end

      it 'is invalid without identity_photo_url in data' do
        video_frame = VideoFrame.new(frame_type: :video_between_protocol_and_identity, data: { 'protocol_photo_url' => 'url' })
        expect(video_frame).not_to be_valid
      end
    end

    context 'when frame_type is video_over_frame' do
      it 'validates presence of frame_url in data' do
        video_frame = VideoFrame.new(frame_type: :video_over_frame, data: { 'frame_url' => 'url' })
        expect(video_frame).to be_valid
      end

      it 'is invalid without frame_url in data' do
        video_frame = VideoFrame.new(frame_type: :video_over_frame, data: {})
        expect(video_frame).not_to be_valid
      end
    end
  end
end
