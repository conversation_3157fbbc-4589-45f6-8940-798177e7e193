require 'rails_helper'

RSpec.describe VideoFramesController, type: :controller do

  before do
    @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
    @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
    @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
    @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)

    @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)

    @token = @user.generate_jwt_token
    @request.headers['Authorization'] = "Bearer #{@token}"
    @request.headers['X-App-Version'] = '2408.13.01'
    @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]

    FactoryBot.create(:video_frame, user_id: @user.id)

  end

  describe "GET #get_video_frames" do
    context "with valid parameters" do
      it "returns a successful response" do
        get :get_video_frames, params: { pagination_params: {  }, count: 1 }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["video_frames"].size).to eq(1)
      end

      it "returns the correct pagination parameters" do
        get :get_video_frames, params: { pagination_params: { last_item_created_at: Time.now.to_s }, count: 10 }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["pagination_params"]["last_item_created_at"]).to be_present
      end

      it "indicates if it is the last page" do
        get :get_video_frames, params: { pagination_params: { last_item_created_at: Time.now.to_s }, count: 20 }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)["is_last_page"]).to be true
      end
    end
  end
end
