# spec/controllers/video_posters_controller_spec.rb
require 'rails_helper'

RSpec.describe VideoPostersController, type: :controller do


  before do
    @state = FactoryBot.create(:circle, circle_type: :location, level: :state)
    @district = FactoryBot.create(:circle, circle_type: :location, level: :district, parent_circle: @state)
    @mandal = FactoryBot.create(:circle, circle_type: :location, level: :mandal, parent_circle: @district)
    @village = FactoryBot.create(:circle, circle_type: :location, level: :village, parent_circle: @mandal)

    @user = FactoryBot.create(:user, village_id: @village.id, mandal_id: @mandal.id, district_id: @district.id, state_id: @state.id)

    @token = @user.generate_jwt_token
    @request.headers['Authorization'] = "Bearer #{@token}"
    @request.headers['X-App-Version'] = '2408.13.01'
    @request.headers['X-Api-Key'] = Rails.application.credentials[:api_key]


  end

  describe 'GET #show' do

    context 'when video_poster is not found' do
      it 'returns not found' do
        get :show, params: { id: -1 }
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['message']).to eq(I18n.t('errors.video_poster_not_found'))
      end
    end

    context 'when video_poster is found' do
      it 'returns the video_poster' do
        video_frame = FactoryBot.create(:video_frame, user_id: @user.id)
        video = FactoryBot.create(:video, user_id: @user.id)
        video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, video: video, user_id: @user.id, source_video_id: video.id)
        get :show, params: { id: video_poster.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['id']).to eq(video_poster.id)
      end
    end
  end

  describe 'POST #generate_video_poster' do
    context 'when video_frame_id or video_id is nil' do
      it 'returns bad request' do
        post :generate_video_poster, params: { }
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq(I18n.t('errors.missing_video_frame_id'))
      end
    end

    context 'when video_frame or video is not found' do
      it 'returns not found' do
        post :generate_video_poster, params: { video_frame_id: -1, video_id: -1 }
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['message']).to eq(I18n.t('errors.video_frame_not_found'))
      end
    end

    context 'when video_poster already exists and is not failed' do
      it 'returns the existing video_poster' do
        video_frame = FactoryBot.create(:video_frame, user_id: @user.id)
        video = FactoryBot.create(:video, user_id: @user.id)
        video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, video: video, user_id: @user.id, source_video_id: video.id)
        video_poster.process!
        post :generate_video_poster, params: { video_frame_id: video_frame.id, video_id: video.id }
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['id']).to eq(video_poster.id)
      end
    end

    context 'when video_poster is created and triggers job' do
      it 'creates a new video_poster and triggers job' do
        video_frame = FactoryBot.create(:video_frame, user_id: @user.id)
        video = FactoryBot.create(:video, user_id: @user.id)
        expect {
          post :generate_video_poster, params: { video_frame_id: video_frame.id, video_id: video.id }
        }.to change(VideoPoster, :count).by(1)
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'POST #video_poster_generation_failed' do
    context 'when job_id is nil' do
      it 'returns bad request' do
        post :video_poster_generation_failed, params: { }
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq(I18n.t('errors.job_id_missing'))
      end
    end

    context 'when video_poster is not found' do
      it 'returns not found' do
        post :video_poster_generation_failed, params: { job_id: -1 }
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['message']).to eq(I18n.t('errors.video_poster_not_found'))
      end
    end

    context 'when video_poster is found and can fail' do
      it 'updates the video_poster status to failed' do
        video_frame = FactoryBot.create(:video_frame, user_id: @user.id)
        video = FactoryBot.create(:video, user_id: @user.id)
        video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, video: video, user_id: @user.id, source_video_id: video.id, status: :failed)
        post :video_poster_generation_failed, params: { job_id: video_poster.job_id, error_code: 'error' }
        expect(response).to have_http_status(:ok)
        expect(video_poster.reload.status).to eq('failed')
      end
    end
  end

  describe 'POST #video_poster_generation_completed' do
    context 'when job_id is nil' do
      it 'returns bad request' do
        post :video_poster_generation_completed, params: { }
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq(I18n.t('errors.job_id_missing'))
        expect(JSON.parse(response.body)['message']).to eq(I18n.t('errors.job_id_missing'))
      end
    end

    context 'when video_poster is not found' do
      it 'returns not found' do
        post :video_poster_generation_completed, params: { job_id: -1 }
        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['message']).to eq(I18n.t('errors.video_poster_not_found'))
      end
    end

    context 'when video_poster is found and can complete' do
      it 'updates the video_poster status to completed' do
        video_frame = FactoryBot.create(:video_frame, user_id: @user.id)
        video = FactoryBot.create(:video, user_id: @user.id)
        video_poster = FactoryBot.create(:video_poster, video_frame: video_frame, video: video, user_id: @user.id, source_video_id: video.id, status: :completed)
        post :video_poster_generation_completed, params: { job_id: video_poster.job_id }
        expect(response).to have_http_status(:ok)
        expect(video_poster.reload.status).to eq('completed')
      end
    end
  end
end
