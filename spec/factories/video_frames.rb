FactoryBot.define do
  factory :video_frame do
    association :user
    active { true }
    supported_video_type { :landscape }
    frame_type { :video_between_protocol_and_identity }
    data {
          {
            'identity_photo_url' => 'https://a-cdn.thecircleapp.in/production/admin-media/32/6d08c1bb-4eff-4236-b9f8-897fc50beafa.png',
            'protocol_photo_url' => 'https://a-cdn.thecircleapp.in/production/admin-media/32/b05763ac-f497-427f-a30f-65f4c0edce4e.png',
            'identityOverlapRatio' => 0.5,
          }
        }
  end
end
